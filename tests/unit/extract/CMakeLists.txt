# tests/unit/extract/CMakeLists.txt

# Extract unit tests
set(EXTRACT_TEST_SOURCES
    compressed_csv_test.cpp
    connection_pool_test.cpp
    csv_extractor_test.cpp
    database_connector_test.cpp
    extract_utils_extended_test.cpp
    extract_utils_test.cpp
    extractor_base_test.cpp
    extractor_factory_test.cpp
    json_extractor_test.cpp
    mysql_connector_test.cpp
    odbc_connector_test.cpp
    platform_utils_test.cpp
    postgresql_connector_test.cpp
)

# Create test executable for each test file
foreach(test_source ${EXTRACT_TEST_SOURCES})
    get_filename_component(test_name ${test_source} NAME_WE)
    
    add_executable(${test_name} ${test_source})
    
    target_link_libraries(${test_name}
        PRIVATE
            omop_extract
            omop_core
            omop_common
            GTest::gtest
            GTest::gtest_main
            yaml-cpp
            fmt::fmt
            Threads::Threads
            ${PostgreSQL_LIBRARIES}
    )
    
    # Add include directories
    target_include_directories(${test_name}
        PRIVATE
            ${CMAKE_SOURCE_DIR}/src/lib
            ${CMAKE_SOURCE_DIR}/src/lib/extract
            ${CMAKE_SOURCE_DIR}/src/lib/core
            ${CMAKE_SOURCE_DIR}/src/lib/common
    )
    
    # Set C++20 standard
    target_compile_features(${test_name} PRIVATE cxx_std_20)
    
    # Add test to CTest
    add_test(NAME extract_${test_name} COMMAND ${test_name})
    
    # Set test properties
    set_tests_properties(extract_${test_name} PROPERTIES
        TIMEOUT 60
        LABELS "extract;unit"
    )
endforeach()

# Create a single executable with all extract tests
add_executable(extract_all_tests ${EXTRACT_TEST_SOURCES})

target_link_libraries(extract_all_tests
    PRIVATE
        omop_extract
        omop_core
        omop_common
        GTest::gtest
        GTest::gtest_main
        yaml-cpp
        fmt::fmt
        Threads::Threads
        ${PostgreSQL_LIBRARIES}
)

target_include_directories(extract_all_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/extract
        ${CMAKE_SOURCE_DIR}/src/lib/core
        ${CMAKE_SOURCE_DIR}/src/lib/common
)

target_compile_features(extract_all_tests PRIVATE cxx_std_20)

# Add comprehensive test
add_test(NAME extract_all_tests COMMAND extract_all_tests)
set_tests_properties(extract_all_tests PROPERTIES
    TIMEOUT 300
    LABELS "extract;unit;all"
)

# Add custom target to run only extract tests
add_custom_target(test_extract
    COMMAND ${CMAKE_CTEST_COMMAND} -L extract --output-on-failure
    DEPENDS extract_all_tests
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running extract module tests"
)

# Code coverage target for extract module
if(ENABLE_COVERAGE)
    add_custom_target(coverage_extract
        COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_BINARY_DIR}/coverage/extract
        COMMAND ${GCOV_EXECUTABLE} -b -c -o ${CMAKE_CURRENT_BINARY_DIR} ${EXTRACT_TEST_SOURCES}
        COMMAND ${LCOV_EXECUTABLE} --capture --directory ${CMAKE_CURRENT_BINARY_DIR} 
                --output-file ${CMAKE_BINARY_DIR}/coverage/extract/coverage.info
        COMMAND ${LCOV_EXECUTABLE} --remove ${CMAKE_BINARY_DIR}/coverage/extract/coverage.info
                '/usr/*' '*/test/*' '*/external/*'
                --output-file ${CMAKE_BINARY_DIR}/coverage/extract/coverage_filtered.info
        COMMAND ${GENHTML_EXECUTABLE} ${CMAKE_BINARY_DIR}/coverage/extract/coverage_filtered.info
                --output-directory ${CMAKE_BINARY_DIR}/coverage/extract/html
        DEPENDS test_extract
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Generating extract module coverage report"
    )
endif()

# Memory leak detection for extract tests
if(ENABLE_SANITIZERS)
    target_compile_options(extract_all_tests PRIVATE
        -fsanitize=address,undefined
        -fno-omit-frame-pointer
    )
    
    target_link_options(extract_all_tests PRIVATE
        -fsanitize=address,undefined
    )
endif()
