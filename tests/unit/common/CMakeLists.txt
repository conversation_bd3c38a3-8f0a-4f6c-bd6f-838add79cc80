# Unit tests for OMOP Common library

# Test source files in the common directory
set(COMMON_TEST_SOURCES
    configuration_test.cpp
    exceptions_test.cpp
    logging_test.cpp
    utilities_test.cpp
    validation_test.cpp
)

# Create individual test executables for each test file
foreach(test_source ${COMMON_TEST_SOURCES})
    # Extract test name from filename (remove .cpp extension)
    get_filename_component(test_name ${test_source} NAME_WE)

    # Create the test executable using the parent function
    create_unit_test_executable(${test_name} ${test_source})

    # Link Common library and required dependencies
    target_link_libraries(${test_name}
        omop_common
        yaml-cpp
        fmt::fmt
    )

    # Add component-specific include directories
    target_include_directories(${test_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/common
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # Set test-specific properties
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/common
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )

    # Add coverage flags if enabled
    if(ENABLE_COVERAGE)
        target_compile_options(${test_name} PRIVATE --coverage)
        target_link_options(${test_name} PRIVATE --coverage)
    endif()
endforeach()

# Create a combined test executable for all Common tests
create_unit_test_executable(test_common_all "${COMMON_TEST_SOURCES}")

# Link Common library and required dependencies for combined test
target_link_libraries(test_common_all
    omop_common
    yaml-cpp
    fmt::fmt
)

# Add component-specific include directories for combined test
target_include_directories(test_common_all PRIVATE
    ${CMAKE_SOURCE_DIR}/src/lib
    ${CMAKE_SOURCE_DIR}/src/lib/common
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Set properties for combined test
set_target_properties(test_common_all PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/common
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
)

# Add coverage flags if enabled for combined test
if(ENABLE_COVERAGE)
    target_compile_options(test_common_all PRIVATE --coverage)
    target_link_options(test_common_all PRIVATE --coverage)
endif()

# Create convenience target to run all Common tests
add_custom_target(run_common_tests
    COMMAND ${CMAKE_CTEST_COMMAND} -L "unit" --output-on-failure
    DEPENDS test_common_all
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running all Common unit tests"
)

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "Common Unit Tests Configuration:")
message(STATUS "  Test files: ${COMMON_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/common")
message(STATUS "  C++ Standard: 20")